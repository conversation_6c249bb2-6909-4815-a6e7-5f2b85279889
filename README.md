# Game Stats Tracker

A Flask-based web application for tracking game statistics across multiple games including Valorant, BGMI, and CS2.

## Features

- **User Management**: Registration, authentication, and profile management
- **Multi-Game Support**: Track stats for Valorant, BGMI, CS2, and more
- **Real-time Stats**: Automatic fetching of recent matches and statistics
- **Interactive Dashboard**: Visual analytics with charts and graphs
- **Achievement System**: Gamified badges and milestones
- **Social Features**: Compare stats with friends and top players
- **OAuth Integration**: Steam and Riot ID authentication

## Tech Stack

- **Backend**: Flask, SQLAlchemy, Celery
- **Database**: PostgreSQL
- **Cache**: Redis
- **Frontend**: HTML5, CSS3, JavaScript, Chart.js
- **APIs**: Riot Games API, Steam Web API
- **Authentication**: Flask-Login, OAuth2

## Quick Start

### Prerequisites

- Python 3.8+
- PostgreSQL
- Redis
- API keys for Riot Games and Steam

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd game_stat_tracker
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. Initialize the database:
```bash
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

6. Start Redis server:
```bash
redis-server
```

7. Start Celery worker (in a separate terminal):
```bash
celery -A app.celery worker --loglevel=info
```

8. Run the application:
```bash
python app.py
```

Visit `http://localhost:5000` to access the application.

## API Keys Setup

### Riot Games API
1. Visit [Riot Developer Portal](https://developer.riotgames.com/)
2. Create an account and generate an API key
3. Add the key to your `.env` file as `RIOT_API_KEY`

### Steam Web API
1. Visit [Steam Web API](https://steamcommunity.com/dev/apikey)
2. Generate an API key
3. Add the key to your `.env` file as `STEAM_API_KEY`

## Development

### Running Tests
```bash
pytest
```

### Code Formatting
```bash
black .
flake8 .
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
