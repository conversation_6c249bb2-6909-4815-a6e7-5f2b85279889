"""
Friendship system model
"""
from datetime import datetime
from app import db


class Friendship(db.Model):
    """Model for user friendships"""
    __tablename__ = 'friendships'
    
    id = db.<PERSON>umn(db.<PERSON>teger, primary_key=True)
    user_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('users.id'), nullable=False)
    friend_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    
    # Friendship status
    status = db.Column(db.String(20), default='pending')  # 'pending', 'accepted', 'blocked'
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Unique constraint to prevent duplicate friendships
    __table_args__ = (
        db.UniqueConstraint('user_id', 'friend_id', name='unique_friendship'),
        db.CheckConstraint('user_id != friend_id', name='no_self_friendship')
    )
    
    def accept(self):
        """Accept the friendship request"""
        self.status = 'accepted'
        self.updated_at = datetime.utcnow()
    
    def block(self):
        """Block the friendship"""
        self.status = 'blocked'
        self.updated_at = datetime.utcnow()
    
    def to_dict(self):
        """Convert friendship to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'friend_id': self.friend_id,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @staticmethod
    def send_friend_request(user_id, friend_id):
        """Send a friend request"""
        # Check if friendship already exists
        existing = Friendship.query.filter(
            db.or_(
                db.and_(Friendship.user_id == user_id, Friendship.friend_id == friend_id),
                db.and_(Friendship.user_id == friend_id, Friendship.friend_id == user_id)
            )
        ).first()
        
        if existing:
            return existing, False  # Already exists
        
        # Create new friendship request
        friendship = Friendship(user_id=user_id, friend_id=friend_id)
        db.session.add(friendship)
        return friendship, True
    
    @staticmethod
    def get_pending_requests(user_id):
        """Get pending friend requests for a user"""
        return Friendship.query.filter_by(friend_id=user_id, status='pending').all()
    
    @staticmethod
    def get_sent_requests(user_id):
        """Get sent friend requests by a user"""
        return Friendship.query.filter_by(user_id=user_id, status='pending').all()
    
    @staticmethod
    def get_friends(user_id):
        """Get all accepted friends for a user"""
        sent_friends = db.session.query(Friendship).filter(
            Friendship.user_id == user_id,
            Friendship.status == 'accepted'
        ).all()
        
        received_friends = db.session.query(Friendship).filter(
            Friendship.friend_id == user_id,
            Friendship.status == 'accepted'
        ).all()
        
        return sent_friends + received_friends
    
    def __repr__(self):
        return f'<Friendship {self.user_id} -> {self.friend_id} ({self.status})>'
